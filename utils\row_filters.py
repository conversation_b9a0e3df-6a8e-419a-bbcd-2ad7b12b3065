# Databricks notebook source
# MAGIC %md
# MAGIC # Row Filtering Functions
# MAGIC 
# MAGIC This notebook contains SQL-based row filtering functions for data security and access control.
# MAGIC Functions are applied based on current_user() permissions and table configurations.

# COMMAND ----------
# -- Row Filter Function for customer_type
spark.sql('''CREATE OR REPLACE FUNCTION filter_customer_type(customer_type STRING)
RETURNS BOOLEAN
RETURN
    CASE 
        WHEN current_user() = '<EMAIL>' THEN TRUE
        ELSE customer_type IS NOT NULL 
             AND customer_type IN ('Retail', 'Corporate', 'VIP') -- Example values, adjust as needed
    END;''')

# COMMAND ----------
# -- Row Filter Function for site_type
spark.sql('''CREATE OR REPLACE FUNCTION filter_site_type(site_type STRING)
RETURNS BOOLEAN
RETURN
    CASE 
        WHEN current_user() = '<EMAIL>' THEN TRUE
        ELSE site_type IS NOT NULL 
             AND site_type IN ('Store', 'Warehouse', 'Office') -- Example values, adjust as needed
    END;''')

# COMMAND ----------
# -- Row Filter Function for region_id
spark.sql('''CREATE OR REPLACE FUNCTION filter_region_id(region_id STRING)
RETURNS BOOLEAN
RETURN
    CASE 
        WHEN current_user() = '<EMAIL>' THEN TRUE
        ELSE region_id IS NOT NULL 
             AND region_id IN ('REG1', 'REG2', 'REG3') -- Example values, adjust as needed
    END;''')