# Databricks notebook source
# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %md
# MAGIC # Customer Silver Layer Processing
# MAGIC
# MAGIC This notebook processes customer data from bronze to silver layer.
# MAGIC It uses the generic utility functions from `utils/silver_utils.py` for:
# MAGIC - Reading bronze data (`read_bronze_data`)
# MAGIC - Writing silver data with schema management (`write_silver_data`)
# MAGIC - Creating/updating Delta tables (`create_or_update_silver_table`)
# MAGIC
# MAGIC The table-specific transformation logic is defined in this notebook.

# COMMAND ----------

# Imports
from pyspark.sql.functions import (
    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace,
    to_date, regexp_extract, datediff
)
from pyspark.sql.types import StringType

# Initialize logger using configuration from startup
logger = create_logger(component_log_levels=component_log_levels)
logger.info("🚀 Initializing silver layer processing notebook")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
processing_date = pipeline_config.get("default_processing_date")
table_name = "dim_customer"  # Can be parameterized for other tables

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

logger.info(f"Configuration initialized with catalog: {catalog}, bronze_schema: {bronze_schema}, silver_schema: {silver_schema}")

# COMMAND ----------
# Simple Transformation Functions
@log_execution(logger)
def transform_data(df, table_name, table_config, logger):
    """Transform data with customer-specific business logic."""
    logger.info(f"Applying customer-specific transformations for {table_name}")

    try:
        # Note: Column mapping is now handled by apply_column_mapping function
        # This function focuses on customer-specific transformations

        # Apply all transformations using SQL expressions for simplicity
        df = df.select(
            # Core customer fields
            col("customer_id"),
            trim(regexp_replace(col("customer_name"), r"\s+", " ")).alias("customer_name"),

            # Standardize customer type
            when(upper(col("customer_type")).isin(["BUSINESS", "B"]), "Business")
            .when(upper(col("customer_type")).isin(["INDIVIDUAL", "I", "PERSON"]), "Individual")
            .when(upper(col("customer_type")).isin(["CORPORATE", "C", "CORP"]), "Corporate")
            .when(upper(col("customer_type")).isin(["GOVERNMENT", "G", "GOV"]), "Government")
            .when(upper(col("customer_type")).isin(["NON-PROFIT", "N", "NONPROFIT"]), "Non-profit")
            .otherwise(col("customer_type")).alias("customer_type"),

            # Clean email and validate
            lower(trim(col("customer_email_address"))).alias("customer_email_address"),

            # Standardize phone number to XXX-XXX-XXXX format
            regexp_replace(
                regexp_replace(col("phone_number").cast(StringType()), r"[^\d]", ""),
                r"(\d{3})(\d{3})(\d{4})", "$1-$2-$3"
            ).alias("phone_number"),

            # Clean address
            col("address"),

            # Convert registration date
            to_date(col("registration_date"), "M/d/yyyy").alias("registration_date"),

            # Convert is_active to boolean
            when(upper(col("is_active")).isin(["TRUE", "1", "ACTIVE", "Y", "YES"]), True)
            .when(upper(col("is_active")).isin(["FALSE", "0", "INACTIVE", "N", "NO"]), False)
            .otherwise(None).alias("is_active"),

            col("loyalty_card_id"),

            # Add validation columns
            (regexp_extract(lower(trim(col("customer_email_address"))), r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 0) != "").alias("email_valid"),

            (regexp_extract(
                regexp_replace(
                    regexp_replace(col("phone_number").cast(StringType()), r"[^\d]", ""),
                    r"(\d{3})(\d{3})(\d{4})", "$1-$2-$3"
                ), r"^\d{3}-\d{3}-\d{4}$", 0
            ) != "").alias("phone_valid"),

            # Extract address components
            regexp_extract(col("address"), r"[A-Z][0-9][A-Z]\s?[0-9][A-Z][0-9]", 0).alias("postal_code"),
            regexp_extract(col("address"), r"\b(AB|BC|MB|NB|NL|NS|NT|NU|ON|PE|QC|SK|YT)\b", 0).alias("province"),
            regexp_extract(col("address"), r", ([^,]+),", 1).alias("city"),

            # Calculate customer tenure
            when(col("registration_date").isNotNull(),
                 datediff(current_timestamp(), to_date(col("registration_date"), "M/d/yyyy"))
            ).otherwise(None).alias("customer_tenure_days"),

            # Data quality validation columns
            when(col("customer_id").isNotNull() & (col("customer_id") != ""), True).otherwise(False).alias("primary_key_valid"),
            when(col("customer_id").isNotNull() & (col("customer_id") != ""), True).otherwise(False).alias("customer_id_not_null_valid"),
            when(col("customer_name").isNotNull() & (col("customer_name") != ""), True).otherwise(False).alias("customer_name_not_null_valid"),
            when(col("customer_type").isNotNull() & (col("customer_type") != ""), True).otherwise(False).alias("customer_type_not_null_valid"),

            # Calculate completeness score based on required fields
            ((when(col("customer_id").isNotNull() & (col("customer_id") != ""), 1).otherwise(0) +
              when(col("customer_name").isNotNull() & (col("customer_name") != ""), 1).otherwise(0) +
              when(col("customer_type").isNotNull() & (col("customer_type") != ""), 1).otherwise(0)) / 3.0).alias("completeness_score"),

            # Calculate overall data quality score
            ((when(col("customer_id").isNotNull() & (col("customer_id") != ""), 1).otherwise(0) +
              when(col("customer_name").isNotNull() & (col("customer_name") != ""), 1).otherwise(0) +
              when((regexp_extract(lower(trim(col("customer_email_address"))), r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 0) != ""), 1).otherwise(0) +
              when(col("customer_type").isNotNull() & (col("customer_type") != ""), 1).otherwise(0)) / 4.0).alias("data_quality_score"),

            # Add audit columns
            current_timestamp().alias("created_timestamp"),
            current_timestamp().alias("modified_timestamp"),
            current_timestamp().alias("processed_at"),
            lit("bronze_layer").alias("source_system")
        )

        logger.info(f"Transformation completed. Columns: {len(df.columns)}")
        return df

    except Exception as e:
        logger.error(f"Error in transformation: {str(e)}")
        raise

# COMMAND ----------
# Main Processing Function (can be reused in other silver notebooks)
@log_execution(logger)
def process_bronze_to_silver(spark, table_name, table_config, catalog, bronze_schema, silver_schema,
                           bronze_path, silver_path, logger, processing_date=None, custom_transform_func=None):
    """
    Generic function to process any table from bronze to silver layer using silver_utils functions.

    Args:
        spark: Spark session
        table_name: Name of the silver table (e.g., 'dim_customer', 'fact_invoice_line')
        table_config: Table configuration dictionary
        catalog: Catalog name
        bronze_schema: Bronze schema name
        silver_schema: Silver schema name
        bronze_path: Bronze storage path
        silver_path: Silver storage path
        logger: Logger instance
        processing_date: Optional processing date for partitioned data
        custom_transform_func: Optional custom transformation function that takes (df, table_name, table_config, logger)

    Returns:
        Transformed DataFrame
    """
    logger.info(f"🚀 Starting bronze to silver processing for {table_name}")

    try:
        # Step 1: Read bronze data using silver_utils function
        bronze_df = read_bronze_data(spark, table_name, catalog, bronze_schema, bronze_path, logger, processing_date)
        if bronze_df.count() == 0:
            logger.warning(f"No bronze data found for {table_name}")
            return None

        # Step 2: Apply column mapping using silver_utils function
        mapped_df = apply_column_mapping(bronze_df, table_name, table_config, logger)

        # Step 3: Apply transformations
        if custom_transform_func:
            logger.info("Applying custom transformation function")
            silver_df = custom_transform_func(mapped_df, table_name, table_config, logger)
        else:
            logger.info("Using table-specific transformation function")
            # Use the table-specific transform_data function for customer
            silver_df = transform_data(mapped_df, table_name, table_config, logger)

        # Step 4: Write to silver layer using silver_utils function
        write_silver_data(spark, silver_df, table_name, table_config, catalog, silver_schema, silver_path, logger)

        logger.info(f"✅ Successfully processed {silver_df.count()} records for {table_name}")
        return silver_df

    except Exception as e:
        logger.error(f"❌ Error processing {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Main execution
logger.info(f"Starting bronze to silver processing for {table_name}")

# Process data using configuration
try:
    result_df = process_bronze_to_silver(
        spark=spark,
        table_name=table_name,
        table_config=table_config,
        catalog=catalog,
        bronze_schema=bronze_schema,
        silver_schema=silver_schema,
        bronze_path=bronze_path,
        silver_path=silver_path,
        logger=logger,
        processing_date=processing_date
    )
    if result_df is not None:
        logger.info(f"✅ Completed bronze to silver processing successfully for {table_name}")
        log_dataframe_info(result_df, f"{table_name}_silver_final", logger)
    else:
        logger.warning(f"⚠️ No data processed for {table_name}")
except Exception as e:
    logger.error(f"❌ Failed to process {table_name}: {str(e)}")
    raise
