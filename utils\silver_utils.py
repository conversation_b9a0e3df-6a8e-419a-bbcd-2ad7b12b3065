# Databricks notebook source
# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %md
# MAGIC # Silver Utils
# MAGIC 
# MAGIC This notebook contains utility functions for silver layer processing.

# COMMAND ----------
# Schema Management Functions
@log_execution(logger)
def create_or_update_silver_table(spark, table_name, table_config, storage_path, logger):
    """Create or update Delta table with complete schema, constraints, and properties."""
    logger.info(f"Managing schema for table {table_name}")

    try:
        # Get configuration
        base_schema = table_config["schema"]
        primary_key = table_config.get("primary_key")

        # Build complete DDL with all constraints
        column_definitions = []
        for col_def in base_schema:
            col_name = col_def["name"]
            col_type = col_def["type"]
            nullable = col_def.get("nullable", True)

            col_ddl = f"{col_name} {col_type}"
            if not nullable:
                col_ddl += " NOT NULL"
            column_definitions.append(col_ddl)

        # Add audit columns
        audit_columns = [
            "created_timestamp TIMESTAMP",
            "modified_timestamp TIMESTAMP",
            "processed_at TIMESTAMP",
            "source_system STRING",
            "data_quality_score DOUBLE",
            "primary_key_valid BOOLEAN",
            "completeness_score DOUBLE"
        ]

        all_columns = column_definitions + audit_columns
        schema_ddl = ",\n            ".join(all_columns)

        # Create complete table with constraints
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            {schema_ddl},
            CONSTRAINT pk_{primary_key} PRIMARY KEY ({primary_key})
        )
        USING DELTA
        LOCATION '{storage_path}'
        TBLPROPERTIES (
            'delta.columnMapping.mode' = 'name',
            'delta.autoOptimize.optimizeWrite' = 'true',
            'delta.autoOptimize.autoCompact' = 'true',
            'delta.enableChangeDataFeed' = 'true'
        )
        """

        logger.info(f"Creating/updating table with DDL")
        spark.sql(create_sql)
        logger.info(f"✅ Table {table_name} ready with all constraints")

    except Exception as e:
        logger.error(f"Error managing table {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Data I/O Functions
@log_execution(logger)
def read_bronze_data(spark, table_name, catalog, bronze_schema, bronze_path, logger, processing_date=None):
    """Read data from bronze layer."""
    logger.info(f"Reading bronze data for {table_name}")

    try:
        if processing_date:
            date_path = processing_date.replace('-', '/')
            source_path = f"{bronze_path}/{table_name.replace('dim_', '')}/{date_path}"
            df = spark.read.format("parquet").load(source_path)
        else:
            source_path = f"{catalog}.{bronze_schema}.{table_name.replace('dim_', '')}"
            df = spark.table(source_path)

        logger.info(f"Read {df.count()} records from bronze for {table_name}")
        return df

    except Exception as e:
        logger.error(f"Error reading bronze data for {table_name}: {str(e)}")
        raise

@log_execution(logger)
def write_silver_data(spark, df, table_name, table_config, catalog, silver_schema, silver_path, logger):
    """Write data to silver layer with proper schema management."""
    logger.info(f"Writing data to silver table {table_name}")

    try:
        # Get configuration for the specific table
        config = table_config[table_name]
        primary_key = config["primary_key"]

        # Construct paths
        full_table_name = f"{catalog}.{silver_schema}.{table_name}"
        target_path = f"{silver_path}/{table_name}"

        # Create or update table schema
        logger.info("Ensuring table schema is up to date")
        create_or_update_silver_table(spark, full_table_name, config, target_path, logger)

        # Write data using merge for upsert behavior
        logger.info("Writing data with upsert logic")

        # Create temporary view for merge
        temp_view_name = f"temp_{table_name}_data"
        df.createOrReplaceTempView(temp_view_name)

        # Use SQL MERGE for clean upsert
        merge_sql = f"""
        MERGE INTO {full_table_name} AS target
        USING {temp_view_name} AS source
        ON target.{primary_key} = source.{primary_key}
        WHEN MATCHED THEN UPDATE SET *
        WHEN NOT MATCHED THEN INSERT *
        """

        spark.sql(merge_sql)
        logger.info("✅ Data written successfully with upsert")

        # Optimize table
        spark.sql(f"OPTIMIZE {full_table_name}")
        logger.info("✅ Table optimized")

        return df

    except Exception as e:
        logger.error(f"Error writing silver data for {table_name}: {str(e)}")
        raise
