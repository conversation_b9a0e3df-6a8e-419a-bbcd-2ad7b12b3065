# Databricks notebook source

# COMMAND ----------

# Imports
from pyspark.sql.functions import col, lit, when, current_timestamp

# COMMAND ----------

# MAGIC %md
# MAGIC # Silver Utils
# MAGIC
# MAGIC This notebook contains generic utility functions for silver layer processing.
# MAGIC These functions can be used across all silver layer notebooks for:
# MAGIC - Reading bronze data with flexible table naming
# MAGIC - Writing silver data with composite primary key support
# MAGIC - Creating/updating Delta tables with proper constraints
# MAGIC - Applying column mappings from bronze to silver
# MAGIC - Adding audit and data quality columns

# COMMAND ----------
# Schema Management Functions
def create_or_update_silver_table(spark, table_name, table_config, storage_path, logger):
    """Create or update Delta table with complete schema, constraints, and properties."""
    logger.info(f"Managing schema for table {table_name}")

    try:
        # Get configuration
        base_schema = table_config["schema"]
        primary_key = table_config.get("primary_key")

        # Build complete DDL with all constraints
        column_definitions = []
        for col_def in base_schema:
            col_name = col_def["name"]
            col_type = col_def["type"]
            nullable = col_def.get("nullable", True)

            col_ddl = f"{col_name} {col_type}"
            if not nullable:
                col_ddl += " NOT NULL"
            column_definitions.append(col_ddl)

        # Add audit columns
        audit_columns = [
            "created_timestamp TIMESTAMP",
            "modified_timestamp TIMESTAMP",
            "processed_at TIMESTAMP",
            "source_system STRING",
            "data_quality_score DOUBLE",
            "primary_key_valid BOOLEAN",
            "completeness_score DOUBLE"
        ]

        all_columns = column_definitions + audit_columns
        schema_ddl = ",\n            ".join(all_columns)

        # Handle primary key - can be single column or composite
        if primary_key:
            if isinstance(primary_key, list):
                # Composite primary key
                pk_columns = ", ".join(primary_key)
                pk_constraint_name = "_".join(primary_key)
            else:
                # Single primary key
                pk_columns = primary_key
                pk_constraint_name = primary_key

            # Create complete table with constraints
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {schema_ddl}
            )
            USING DELTA
            LOCATION '{storage_path}'
            TBLPROPERTIES (
                'delta.columnMapping.mode' = 'name',
                'delta.autoOptimize.optimizeWrite' = 'true',
                'delta.autoOptimize.autoCompact' = 'true',
                'delta.enableChangeDataFeed' = 'true'
            )
            """
        else:
            # No primary key defined
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {schema_ddl}
            )
            USING DELTA
            LOCATION '{storage_path}'
            TBLPROPERTIES (
                'delta.columnMapping.mode' = 'name',
                'delta.autoOptimize.optimizeWrite' = 'true',
                'delta.autoOptimize.autoCompact' = 'true',
                'delta.enableChangeDataFeed' = 'true'
            )
            """

        logger.info(f"Creating/updating table with DDL")
        spark.sql(create_sql)

        # Add primary key constraint separately (Delta Lake requirement)
        if primary_key:
            try:
                alter_sql = f"ALTER TABLE {table_name} ADD CONSTRAINT pk_{pk_constraint_name} PRIMARY KEY ({pk_columns})"
                spark.sql(alter_sql)
                logger.info(f"✅ Primary key constraint added for {table_name}")
            except Exception as pk_error:
                # Primary key constraint might already exist
                logger.warning(f"Primary key constraint may already exist: {str(pk_error)}")

        logger.info(f"✅ Table {table_name} ready with all constraints")

    except Exception as e:
        logger.error(f"Error managing table {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Data I/O Functions
def read_bronze_data(spark, table_name, catalog, bronze_schema, bronze_path, logger, processing_date=None):
    """Read data from bronze layer with support for different table naming patterns."""
    logger.info(f"Reading bronze data for {table_name}")

    try:
        # Determine bronze table name based on silver table name
        if table_name.startswith("dim_"):
            bronze_table_name = table_name.replace("dim_", "")
        elif table_name.startswith("fact_"):
            bronze_table_name = table_name.replace("fact_", "")
        else:
            # For tables that don't follow dim_/fact_ pattern
            bronze_table_name = table_name

        if processing_date:
            # Read from file path with date partitioning
            date_path = processing_date.replace('-', '/')
            source_path = f"{bronze_path}/{bronze_table_name}/{date_path}"
            logger.info(f"Reading from file path: {source_path}")
            df = spark.read.format("parquet").load(source_path)
        else:
            # Read from bronze table
            source_path = f"{catalog}.{bronze_schema}.{bronze_table_name}"
            logger.info(f"Reading from bronze table: {source_path}")
            df = spark.table(source_path)

        record_count = df.count()
        logger.info(f"Read {record_count} records from bronze for {table_name}")
        return df

    except Exception as e:
        logger.error(f"Error reading bronze data for {table_name}: {str(e)}")
        raise

def write_silver_data(spark, df, table_name, table_config, catalog, silver_schema, silver_path, logger):
    """Write data to silver layer with proper schema management and support for composite primary keys."""
    logger.info(f"Writing data to silver table {table_name}")

    try:
        # Get configuration for the specific table
        config = table_config[table_name]
        primary_key = config.get("primary_key")

        # Construct paths
        full_table_name = f"{catalog}.{silver_schema}.{table_name}"
        target_path = f"{silver_path}/{table_name}"

        # Create or update table schema
        logger.info("Ensuring table schema is up to date")
        create_or_update_silver_table(spark, full_table_name, config, target_path, logger)

        # Write data using merge for upsert behavior if primary key exists
        if primary_key:
            logger.info("Writing data with upsert logic")

            # Create temporary view for merge
            temp_view_name = f"temp_{table_name}_data"
            df.createOrReplaceTempView(temp_view_name)

            # Build merge condition for single or composite primary keys
            if isinstance(primary_key, list):
                # Composite primary key
                merge_conditions = " AND ".join([f"target.{pk} = source.{pk}" for pk in primary_key])
            else:
                # Single primary key
                merge_conditions = f"target.{primary_key} = source.{primary_key}"

            # Use SQL MERGE for clean upsert
            merge_sql = f"""
            MERGE INTO {full_table_name} AS target
            USING {temp_view_name} AS source
            ON {merge_conditions}
            WHEN MATCHED THEN UPDATE SET *
            WHEN NOT MATCHED THEN INSERT *
            """

            spark.sql(merge_sql)
            logger.info("✅ Data written successfully with upsert")
        else:
            # No primary key - use overwrite mode
            logger.info("No primary key defined - using overwrite mode")
            df.write.format("delta").mode("overwrite").option("path", target_path).saveAsTable(full_table_name)
            logger.info("✅ Data written successfully with overwrite")

        # Optimize table
        spark.sql(f"OPTIMIZE {full_table_name}")
        logger.info("✅ Table optimized")

        return df

    except Exception as e:
        logger.error(f"Error writing silver data for {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Generic Column Mapping Function
def apply_column_mapping(df, table_name, table_config, logger):
    """Apply column mapping from bronze to silver based on table configuration."""
    logger.info(f"Applying column mapping for {table_name}")

    try:
        # Get configuration for the specific table
        config = table_config[table_name]
        column_mapping = config.get("column_mapping_bronze_to_silver", {})

        if not column_mapping:
            logger.warning(f"No column mapping found for {table_name}, returning original DataFrame")
            return df

        # Apply column mapping
        select_expressions = []
        for bronze_col, silver_col in column_mapping.items():
            if bronze_col in df.columns:
                select_expressions.append(col(bronze_col).alias(silver_col))
            else:
                logger.warning(f"Bronze column '{bronze_col}' not found in DataFrame for {table_name}")

        if select_expressions:
            df = df.select(*select_expressions)
            logger.info(f"Applied column mapping: {len(select_expressions)} columns mapped")
        else:
            logger.warning(f"No valid column mappings found for {table_name}")

        return df

    except Exception as e:
        logger.error(f"Error applying column mapping for {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Generic Data Quality Functions
def add_audit_columns(df, source_system="bronze_layer"):
    """Add standard audit columns to DataFrame."""
    from pyspark.sql.functions import current_timestamp, lit

    return df.withColumn("created_timestamp", current_timestamp()) \
             .withColumn("modified_timestamp", current_timestamp()) \
             .withColumn("processed_at", current_timestamp()) \
             .withColumn("source_system", lit(source_system))

def add_data_quality_columns(df, table_name, table_config):
    """Add basic data quality validation columns."""
    from pyspark.sql.functions import when, col

    config = table_config[table_name]
    primary_key = config.get("primary_key")

    # Add primary key validation
    if primary_key:
        if isinstance(primary_key, list):
            # Composite primary key - check all parts are not null
            pk_valid_conditions = [
                (col(pk).isNotNull() & (col(pk) != "")) for pk in primary_key
            ]
            pk_valid = pk_valid_conditions[0]
            for condition in pk_valid_conditions[1:]:
                pk_valid = pk_valid & condition
        else:
            # Single primary key
            pk_valid = col(primary_key).isNotNull() & (col(primary_key) != "")

        df = df.withColumn("primary_key_valid", when(pk_valid, True).otherwise(False))

    # Add basic completeness score (can be customized per table)
    df = df.withColumn("completeness_score", lit(1.0))  # Default to 1.0, override in table-specific logic
    df = df.withColumn("data_quality_score", lit(1.0))  # Default to 1.0, override in table-specific logic

    return df
