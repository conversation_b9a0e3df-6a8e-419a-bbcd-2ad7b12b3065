# Databricks notebook source
# MAGIC %md
# # Enhanced Logger Utility with Color Support
# 
# This notebook provides logging functionality for the data pipeline using Python's built-in logging module with color-coded output for better readability.

# COMMAND ----------
import logging
import os
import sys
import inspect
import functools
import time
from pyspark.sql import SparkSession

# Import log_path from config
try:
    from config.config import log_path
except ImportError:
    # Fallback if import fails
    log_path = None

# Get the Spark session
spark = SparkSession.builder.getOrCreate()

# COMMAND ----------
def get_current_notebook_name():
    """
    Automatically detect the current notebook name from the Databricks context.
    Returns the notebook name without the file extension.
    """
    try:
        # First try to get the notebook path from Databricks context
        # Check if we're in a Databricks environment
        if 'dbutils' in locals() or 'dbutils' in globals():
            notebook_path = dbutils.notebook.entry_point.getDbutils().notebook().getContext().notebookPath().get()
            return os.path.basename(notebook_path).split('.')[0]
        
        # If not in Databricks, try to get the calling module's filename
        frame = inspect.currentframe()
        while frame:
            if frame.f_code.co_filename != __file__:
                caller_file = frame.f_code.co_filename
                return os.path.basename(caller_file).split('.')[0]
            frame = frame.f_back
        
        # If all else fails, try to get the main module's filename
        main_module = sys.modules['__main__'].__file__
        if main_module:
            return os.path.basename(main_module).split('.')[0]
            
        return "unknown_notebook"
    except Exception as e:
        # Fallback in case of any issues
        print(f"Warning: Could not automatically detect notebook name: {str(e)}")
        return "unknown_notebook"

# COMMAND ----------
class NotebookFormatter(logging.Formatter):
    """
    Custom formatter that includes notebook name in the log message.
    """
    def __init__(self, notebook_name, fmt=None, datefmt=None, style='%'):
        if fmt is None:
            fmt = "[%(asctime)s] [%(levelname)s] [" + notebook_name + "] [%(name)s] %(message)s"
        super().__init__(fmt, datefmt, style)

class ColoredNotebookFormatter(logging.Formatter):
    """
    Custom formatter that includes notebook name in the log message with color coding.
    """
    # ANSI color codes using Unicode escape sequences
    COLORS = {
        'RESET': '\u001b[0m',
        'BLACK': '\u001b[30m',
        'RED': '\u001b[31m',
        'GREEN': '\u001b[32m',
        'YELLOW': '\u001b[33m',
        'BLUE': '\u001b[34m',
        'MAGENTA': '\u001b[35m',
        'CYAN': '\u001b[36m',
        'WHITE': '\u001b[37m',
        'BOLD': '\u001b[1m',
        'UNDERLINE': '\u001b[4m',
        'BACKGROUND_BLACK': '\u001b[40m',
        'BACKGROUND_RED': '\u001b[41m',
        'BACKGROUND_GREEN': '\u001b[42m',
        'BACKGROUND_YELLOW': '\u001b[43m',
        'BACKGROUND_BLUE': '\u001b[44m',
        'BACKGROUND_MAGENTA': '\u001b[45m',
        'BACKGROUND_CYAN': '\u001b[46m',
        'BACKGROUND_WHITE': '\u001b[47m',
    }
    
    # Log level colors
    LEVEL_COLORS = {
        'DEBUG': COLORS['BLUE'],
        'INFO': COLORS['GREEN'],
        'WARNING': COLORS['YELLOW'],
        'ERROR': COLORS['RED'],
        'CRITICAL': COLORS['RED'] + COLORS['BOLD'],
    }
    
    # Component colors
    COMPONENT_COLORS = {
        'data_loading': COLORS['CYAN'],
        'transformation': COLORS['MAGENTA'],
        'data_writing': COLORS['YELLOW'],
    }
    
    def __init__(self, notebook_name, fmt=None, datefmt=None, style='%', use_colors=True):
        if fmt is None:
            fmt = "[%(asctime)s] [%(levelname)s] [" + notebook_name + "] [%(name)s] %(message)s"
        self.use_colors = use_colors
        self.notebook_name = notebook_name
        super().__init__(fmt, datefmt, style)
        
    def format(self, record):
        if not self.use_colors:
            return super().format(record)
            
        # Save the original format
        original_fmt = self._style._fmt
        
        # Apply color based on log level
        levelname = record.levelname
        level_color = self.LEVEL_COLORS.get(levelname, self.COLORS['RESET'])
        
        # Check if this is a component logger
        component_color = self.COLORS['RESET']
        if '.' in record.name:
            component = record.name.split('.')[-1]
            component_color = self.COMPONENT_COLORS.get(component, self.COLORS['RESET'])
        
        # Apply colors to different parts of the log message
        colored_fmt = original_fmt.replace(
            "[%(asctime)s]", 
            f"{self.COLORS['CYAN']}[%(asctime)s]{self.COLORS['RESET']}"
        )
        
        colored_fmt = colored_fmt.replace(
            "[%(levelname)s]", 
            f"{level_color}[%(levelname)s]{self.COLORS['RESET']}"
        )
        
        colored_fmt = colored_fmt.replace(
            f"[{self.notebook_name}]", 
            f"{self.COLORS['BOLD']}[{self.notebook_name}]{self.COLORS['RESET']}"
        )
        
        # Apply color to component in the name
        if '.' in record.name:
            base_name, component = record.name.rsplit('.', 1)
            colored_fmt = colored_fmt.replace(
                f"[%(name)s]", 
                f"[{base_name}.{component_color}{component}{self.COLORS['RESET']}]"
            )
        
        # Set the colored format
        self._style._fmt = colored_fmt
        
        # Format the record with colors
        result = super().format(record)
        
        # Restore the original format
        self._style._fmt = original_fmt
        
        return result

# COMMAND ----------
def create_logger(notebook_name=None, log_level="INFO", log_file=None, component_log_levels=None, custom_log_path=None):
    """
    Create a logger for a notebook using Python's built-in logging module.
    
    Args:
        notebook_name: Name of the notebook (if None, auto-detected)
        log_level: Default log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional path to a log file. If None and custom_log_path is None, 
                 will use the log_path from config if available
        component_log_levels: Optional dict mapping component names to log levels
                             e.g., {"data_loading": "DEBUG", "transformation": "INFO"}
        custom_log_path: Optional custom path to store logs. Overrides the default log_path from config
    
    Returns:
        A configured logger instance
    """
    # Auto-detect notebook name if not provided
    if notebook_name is None:
        notebook_name = get_current_notebook_name()
    
    # Convert string log level to logging constant
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Invalid log level: {log_level}")
    
    # Create logger
    logger = logging.getLogger(notebook_name)
    logger.setLevel(numeric_level)
    logger.handlers = []  # Remove any existing handlers
    
    # Create console handler with colored formatter
    console_handler = logging.StreamHandler()
    console_handler.setLevel(numeric_level)
    console_formatter = ColoredNotebookFormatter(notebook_name, use_colors=True)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # Determine log file path if not explicitly provided
    if not log_file:
        # Use custom_log_path if provided, otherwise use log_path from config
        base_log_path = custom_log_path or log_path
        
        if base_log_path:
            # Create a log file name based on the notebook name
            log_file = f"{base_log_path}/{notebook_name}.log"
    
    # Create file handler if log_file is specified
    if log_file:
        # Create directory if it doesn't exist
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(numeric_level)
        # Use regular formatter for file logs (no colors in files)
        file_formatter = NotebookFormatter(notebook_name)
        file_handler.setFormatter(file_formatter)
        # logger.addHandler(file_handler)
    
    # Set up component loggers if specified
    if component_log_levels:
        for component, level in component_log_levels.items():
            component_logger = logging.getLogger(f"{notebook_name}.{component}")
            component_numeric_level = getattr(logging, level.upper(), None)
            if isinstance(component_numeric_level, int):
                component_logger.setLevel(component_numeric_level)
    
    # Add convenience methods for logging execution start/end with enhanced styling
    def log_start(operation, component=None):
        start_msg = "▶️ Starting " + str(operation)
        if component:
            logging.getLogger(f"{notebook_name}.{component}").info(start_msg)
        else:
            logger.info(start_msg)
    
    def log_end(operation, duration=None, component=None):
        if duration is not None:
            duration_str = f" (Duration: {duration:.2f}s)"
        else:
            duration_str = ""
        end_msg = "✅ Completed " + str(operation) + duration_str
        if component:
            logging.getLogger(f"{notebook_name}.{component}").info(end_msg)
        else:
            logger.info(end_msg)
    
    # Attach convenience methods to the logger
    logger.log_start = log_start
    logger.log_end = log_end
    
    return logger

# COMMAND ----------
def log_execution(logger):
    """
    Decorator factory to log function execution time and status.
    
    Args:
        logger: The logger instance to use for logging
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            function_name = func.__name__
            
            # Try to determine the component from the function name
            component = None
            if "load" in function_name or "read" in function_name:
                component = "data_loading"
            elif "transform" in function_name:
                component = "transformation"
            elif "write" in function_name or "save" in function_name or "upsert" in function_name:
                component = "data_writing"
            
            # Get the appropriate logger
            log = logger
            if component:
                component_logger = logging.getLogger(f"{logger.name}.{component}")
                if component_logger.level != logging.NOTSET:  # If component logger has a specific level
                    log = component_logger
            
            # Format arguments for logging
            arg_list = [str(a) for a in args[1:]] if len(args) > 1 else []
            kwarg_list = [f"{k}={v}" for k, v in kwargs.items()]
            arg_str = ", ".join(arg_list + kwarg_list)
            
            log.info(f"🔄 Executing {function_name}({arg_str})")
            start = time.perf_counter()
            try:
                result = func(*args, **kwargs)
                duration = time.perf_counter() - start
                log.info(f"✅ Successfully executed {function_name} in {round(duration, 2)}s")
                return result
            except Exception as e:
                duration = time.perf_counter() - start
                log.error(f"❌ Error in {function_name} after {round(duration, 2)}s: {str(e)}")
                raise
        return wrapper
    return decorator

# COMMAND ----------
def log_dataframe_info(df, name, logger, component=None):
    """
    Log information about a DataFrame.
    
    Args:
        df: The DataFrame to log information about
        name: A name to identify the DataFrame in logs
        logger: The logger instance to use for logging
        component: Optional component name for component-specific logging
    """
    # Get the appropriate logger
    log = logger
    if component:
        component_logger = logging.getLogger(f"{logger.name}.{component}")
        if component_logger.level != logging.NOTSET:  # If component logger has a specific level
            log = component_logger
    
    count = df.count()
    columns = len(df.columns)
    log.info(f"📊 DataFrame '{name}' has {count} rows and {columns} columns")
    
    # Log schema details at debug level
    schema_str = "\n  " + "\n  ".join([f"{field.name}: {field.dataType}" for field in df.schema.fields])
    log.debug(f"🔍 DataFrame '{name}' schema: {schema_str}")
    
    # Log sample data at debug level
    if log.isEnabledFor(logging.DEBUG) and count > 0:
        try:
            sample = df.limit(5).toPandas()
            log.debug(f"🔍 DataFrame '{name}' sample data:\n{sample}")
        except Exception as e:
            log.debug(f"Could not convert sample to pandas: {str(e)}")
    
    return df


