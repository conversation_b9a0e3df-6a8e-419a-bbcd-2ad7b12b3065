# Databricks notebook source
# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %md
# MAGIC # Product Silver Layer Processing
# MAGIC
# MAGIC This notebook processes product data from bronze to silver layer.
# MAGIC It uses the generic utility functions from `utils/silver_utils.py` for:
# MAGIC - Reading bronze data (`read_bronze_data`)
# MAGIC - Writing silver data with schema management (`write_silver_data`)
# MAGIC - Creating/updating Delta tables (`create_or_update_silver_table`)
# MAGIC - Applying column mappings (`apply_column_mapping`)
# MAGIC
# MAGIC The table-specific transformation logic is defined in this notebook.

# COMMAND ----------

# Imports
from pyspark.sql.functions import (
    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace, 
    to_date, regexp_extract, datediff
)
from pyspark.sql.types import StringType

# Initialize logger using configuration from startup
logger = create_logger(component_log_levels=component_log_levels)
logger.info("🚀 Initializing product silver layer processing")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
table_name = "dim_product"

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

logger.info(f"Configuration initialized with catalog: {catalog}, bronze_schema: {bronze_schema}, silver_schema: {silver_schema}")

# COMMAND ----------
# Simple Transformation Functions
@log_execution(logger)
def transform_data(df, table_name, table_config, logger):
    """Transform product data with product-specific business logic."""
    logger.info(f"Applying product-specific transformations for {table_name}")

    try:
        # Note: Column mapping is now handled by apply_column_mapping function
        # This function focuses on product-specific transformations
        
        # Apply all transformations using SQL expressions for simplicity
        df = df.select(
            # Core product fields
            col("product_id"),
            trim(regexp_replace(col("product_name"), r"\s+", " ")).alias("product_name"),
            
            # Standardize product category
            when(upper(col("product_category")).isin(["GROCERY", "GROCERIES"]), "Grocery")
            .when(upper(col("product_category")).isin(["DAIRY", "DAIRY_PRODUCTS"]), "Dairy")
            .when(upper(col("product_category")).isin(["MEAT", "MEATS"]), "Meat")
            .when(upper(col("product_category")).isin(["PRODUCE", "FRUITS_VEGETABLES"]), "Produce")
            .when(upper(col("product_category")).isin(["BAKERY", "BAKED_GOODS"]), "Bakery")
            .when(upper(col("product_category")).isin(["FROZEN", "FROZEN_FOODS"]), "Frozen")
            .when(upper(col("product_category")).isin(["BEVERAGES", "DRINKS"]), "Beverages")
            .when(upper(col("product_category")).isin(["SNACKS", "SNACK_FOODS"]), "Snacks")
            .when(upper(col("product_category")).isin(["HEALTH", "HEALTH_BEAUTY"]), "Health & Beauty")
            .when(upper(col("product_category")).isin(["HOUSEHOLD", "CLEANING"]), "Household")
            .otherwise(col("product_category")).alias("product_category"),
            
            # Clean product sub category
            trim(col("product_sub_category")).alias("product_sub_category"),
            
            # Clean product description
            trim(col("product_description")).alias("product_description"),
            
            # Clean brand name
            trim(regexp_replace(col("brand_name"), r"\s+", " ")).alias("brand_name"),
            
            # Clean allergen information
            trim(col("allergen_information")).alias("allergen_information"),
            
            # Validate shelf life days
            when(col("shelf_life_days").isNotNull() & (col("shelf_life_days") >= 0), col("shelf_life_days"))
            .otherwise(None).alias("shelf_life_days"),
            
            # Convert boolean fields
            when(upper(col("is_private_label")).isin(["TRUE", "1", "Y", "YES"]), True)
            .when(upper(col("is_private_label")).isin(["FALSE", "0", "N", "NO"]), False)
            .otherwise(None).alias("is_private_label"),
            
            when(upper(col("is_organic")).isin(["TRUE", "1", "Y", "YES"]), True)
            .when(upper(col("is_organic")).isin(["FALSE", "0", "N", "NO"]), False)
            .otherwise(None).alias("is_organic"),
            
            when(upper(col("is_perishable")).isin(["TRUE", "1", "Y", "YES"]), True)
            .when(upper(col("is_perishable")).isin(["FALSE", "0", "N", "NO"]), False)
            .otherwise(None).alias("is_perishable"),
            
            when(upper(col("is_active")).isin(["TRUE", "1", "ACTIVE", "Y", "YES"]), True)
            .when(upper(col("is_active")).isin(["FALSE", "0", "INACTIVE", "N", "NO"]), False)
            .otherwise(None).alias("is_active"),
            
            # Add validation columns
            when(col("shelf_life_days").isNotNull(),
                 when(col("shelf_life_days") >= 0, True).otherwise(False)
            ).otherwise(None).alias("shelf_life_valid"),
            
            when(col("allergen_information").isNotNull() & (col("allergen_information") != ""),
                 True
            ).otherwise(False).alias("has_allergen_info"),
            
            # Data quality validation columns
            when(col("product_id").isNotNull() & (col("product_id") != ""), True).otherwise(False).alias("primary_key_valid"),
            when(col("product_id").isNotNull() & (col("product_id") != ""), True).otherwise(False).alias("product_id_not_null_valid"),
            when(col("product_name").isNotNull() & (col("product_name") != ""), True).otherwise(False).alias("product_name_not_null_valid"),
            when(col("product_category").isNotNull() & (col("product_category") != ""), True).otherwise(False).alias("product_category_not_null_valid"),
            
            # Calculate completeness score based on required fields
            ((when(col("product_id").isNotNull() & (col("product_id") != ""), 1).otherwise(0) +
              when(col("product_name").isNotNull() & (col("product_name") != ""), 1).otherwise(0) +
              when(col("product_category").isNotNull() & (col("product_category") != ""), 1).otherwise(0)) / 3.0).alias("completeness_score"),
            
            # Calculate overall data quality score
            ((when(col("product_id").isNotNull() & (col("product_id") != ""), 1).otherwise(0) +
              when(col("product_name").isNotNull() & (col("product_name") != ""), 1).otherwise(0) +
              when(col("product_category").isNotNull() & (col("product_category") != ""), 1).otherwise(0) +
              when(col("brand_name").isNotNull() & (col("brand_name") != ""), 1).otherwise(0)) / 4.0).alias("data_quality_score"),
            
            # Add audit columns
            current_timestamp().alias("created_timestamp"),
            current_timestamp().alias("modified_timestamp"),
            current_timestamp().alias("processed_at"),
            lit("bronze_layer").alias("source_system")
        )
        
        logger.info(f"Transformation completed. Columns: {len(df.columns)}")
        return df
        
    except Exception as e:
        logger.error(f"Error in transformation: {str(e)}")
        raise

# COMMAND ----------
# Main Processing Function (can be reused in other silver notebooks)
@log_execution(logger)
def process_bronze_to_silver(spark, table_name, table_config, catalog, bronze_schema, silver_schema,
                           bronze_path, silver_path, logger, processing_date=None, custom_transform_func=None):
    """
    Generic function to process any table from bronze to silver layer using silver_utils functions.

    Args:
        spark: Spark session
        table_name: Name of the silver table (e.g., 'dim_product', 'dim_item')
        table_config: Table configuration dictionary
        catalog: Catalog name
        bronze_schema: Bronze schema name
        silver_schema: Silver schema name
        bronze_path: Bronze storage path
        silver_path: Silver storage path
        logger: Logger instance
        processing_date: Optional processing date for partitioned data
        custom_transform_func: Optional custom transformation function that takes (df, table_name, table_config, logger)

    Returns:
        Transformed DataFrame
    """
    logger.info(f"🚀 Starting bronze to silver processing for {table_name}")

    try:
        # Step 1: Read bronze data using silver_utils function
        bronze_df = read_bronze_data(spark, table_name, catalog, bronze_schema, bronze_path, logger, processing_date)
        if bronze_df.count() == 0:
            logger.warning(f"No bronze data found for {table_name}")
            return None

        # Step 2: Apply column mapping using silver_utils function
        mapped_df = apply_column_mapping(bronze_df, table_name, table_config, logger)

        # Step 3: Apply transformations
        if custom_transform_func:
            logger.info("Applying custom transformation function")
            silver_df = custom_transform_func(mapped_df, table_name, table_config, logger)
        else:
            logger.info("Using table-specific transformation function")
            # Use the table-specific transform_data function for product
            silver_df = transform_data(mapped_df, table_name, table_config, logger)

        # Step 4: Write to silver layer using silver_utils function
        write_silver_data(spark, silver_df, table_name, table_config, catalog, silver_schema, silver_path, logger)

        logger.info(f"✅ Successfully processed {silver_df.count()} records for {table_name}")
        return silver_df

    except Exception as e:
        logger.error(f"❌ Error processing {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Main execution
logger.info(f"Starting bronze to silver processing for {table_name}")

# Get processing date from configuration
processing_date = pipeline_config.get("default_processing_date")

# Process data using configuration
try:
    result_df = process_bronze_to_silver(
        spark=spark,
        table_name=table_name,
        table_config=table_config,
        catalog=catalog,
        bronze_schema=bronze_schema,
        silver_schema=silver_schema,
        bronze_path=bronze_path,
        silver_path=silver_path,
        logger=logger,
        processing_date=processing_date
    )
    if result_df is not None:
        logger.info(f"✅ Completed bronze to silver processing successfully for {table_name}")
        log_dataframe_info(result_df, f"{table_name}_silver_final", logger)
    else:
        logger.warning(f"⚠️ No data processed for {table_name}")
except Exception as e:
    logger.error(f"❌ Failed to process {table_name}: {str(e)}")
    raise
