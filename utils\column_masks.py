# Databricks notebook source
# MAGIC %md
# MAGIC # Column Masking Functions
# MAGIC 
# MAGIC This notebook contains SQL-based column masking functions for data security and privacy.
# MAGIC Functions are applied based on current_user() permissions and table configurations.

# COMMAND ----------

# -- Email Masking Function
CREATE OR REPLACE FUNCTION mask_email(email STRING)
RETURNS STRING
RETURN
    CASE 
        WHEN current_user() = '<EMAIL>' THEN email
        WHEN email IS NOT NULL THEN CONCAT('***@', SPLIT_PART(email, '@', 2))
        ELSE NULL
    END;

# -- General PII Masking Function (for names and addresses)
CREATE OR REPLACE FUNCTION mask_pii(value STRING)
RETURNS STRING
RETURN
    CASE 
        WHEN current_user() = '<EMAIL>' THEN value
        WHEN value IS NOT NULL THEN SHA2(value, 256)
        ELSE NULL
    END;

# -- Phone Number Masking Function (for xxx-xxx-xxxx format)
CREATE OR REPLACE FUNCTION mask_phone(phone STRING)
RETURNS STRING
RETURN
    CASE 
        WHEN current_user() = '<EMAIL>' THEN phone
        WHEN phone IS NOT NULL THEN CONCAT('***-***-', SUBSTR(phone, 9, 4))
        ELSE NULL
    END;