# Databricks notebook source
# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %md
# MAGIC # Invoice Silver Layer Processing
# MAGIC
# MAGIC This notebook processes invoice data from bronze to silver layer.
# MAGIC It uses the generic utility functions from `utils/silver_utils.py` for:
# MAGIC - Reading bronze data (`read_bronze_data`)
# MAGIC - Writing silver data with schema management (`write_silver_data`)
# MAGIC - Creating/updating Delta tables (`create_or_update_silver_table`)
# MAGIC - Applying column mappings (`apply_column_mapping`)
# MAGIC
# MAGIC The table-specific transformation logic is defined in this notebook.

# COMMAND ----------

# Imports
from pyspark.sql.functions import (
    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace, 
    to_date, regexp_extract, datediff
)
from pyspark.sql.types import StringType

# Initialize logger using configuration from startup
logger = create_logger(component_log_levels=component_log_levels)
logger.info("🚀 Initializing invoice silver layer processing")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
table_name = "dim_invoice"

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

logger.info(f"Configuration initialized with catalog: {catalog}, bronze_schema: {bronze_schema}, silver_schema: {silver_schema}")

# COMMAND ----------
# Simple Transformation Functions
@log_execution(logger)
def transform_data(df, table_name, table_config, logger):
    """Transform invoice data with invoice-specific business logic."""
    logger.info(f"Applying invoice-specific transformations for {table_name}")

    try:
        # Note: Column mapping is now handled by apply_column_mapping function
        # This function focuses on invoice-specific transformations
        
        # Apply all transformations using SQL expressions for simplicity
        df = df.select(
            # Core invoice fields
            col("invoice_id"),
            
            # Standardize invoice type
            when(upper(col("invoice_type")).isin(["SALE", "S"]), "Sale")
            .when(upper(col("invoice_type")).isin(["RETURN", "R"]), "Return")
            .when(upper(col("invoice_type")).isin(["REFUND", "RF"]), "Refund")
            .when(upper(col("invoice_type")).isin(["EXCHANGE", "E"]), "Exchange")
            .otherwise(col("invoice_type")).alias("invoice_type"),
            
            # Standardize status
            when(upper(col("status")).isin(["COMPLETED", "COMPLETE", "C"]), "Completed")
            .when(upper(col("status")).isin(["PENDING", "P"]), "Pending")
            .when(upper(col("status")).isin(["CANCELLED", "CANCELED", "X"]), "Cancelled")
            .when(upper(col("status")).isin(["PROCESSING", "PROC"]), "Processing")
            .otherwise(col("status")).alias("status"),
            
            # Clean reference number
            trim(upper(col("reference_number"))).alias("reference_number"),
            
            col("employee_id"),
            
            # Standardize payment method
            when(upper(col("payment_method")).isin(["CASH", "C"]), "Cash")
            .when(upper(col("payment_method")).isin(["CREDIT", "CC", "CREDIT_CARD"]), "Credit Card")
            .when(upper(col("payment_method")).isin(["DEBIT", "DC", "DEBIT_CARD"]), "Debit Card")
            .when(upper(col("payment_method")).isin(["CHEQUE", "CHECK", "CH"]), "Cheque")
            .when(upper(col("payment_method")).isin(["GIFT_CARD", "GC"]), "Gift Card")
            .otherwise(col("payment_method")).alias("payment_method"),
            
            # Standardize channel type
            when(upper(col("channel_type")).isin(["ONLINE", "WEB", "O"]), "Online")
            .when(upper(col("channel_type")).isin(["INSTORE", "IN_STORE", "STORE", "S"]), "In-Store")
            .when(upper(col("channel_type")).isin(["MOBILE", "APP", "M"]), "Mobile")
            .when(upper(col("channel_type")).isin(["PHONE", "TELEPHONE", "P"]), "Phone")
            .otherwise(col("channel_type")).alias("channel_type"),
            
            # Data quality validation columns
            when(col("invoice_id").isNotNull() & (col("invoice_id") != ""), True).otherwise(False).alias("primary_key_valid"),
            when(col("invoice_id").isNotNull() & (col("invoice_id") != ""), True).otherwise(False).alias("invoice_id_not_null_valid"),
            when(col("invoice_type").isNotNull() & (col("invoice_type") != ""), True).otherwise(False).alias("invoice_type_not_null_valid"),
            when(col("status").isNotNull() & (col("status") != ""), True).otherwise(False).alias("status_not_null_valid"),
            
            # Calculate completeness score based on required fields
            ((when(col("invoice_id").isNotNull() & (col("invoice_id") != ""), 1).otherwise(0) +
              when(col("invoice_type").isNotNull() & (col("invoice_type") != ""), 1).otherwise(0) +
              when(col("status").isNotNull() & (col("status") != ""), 1).otherwise(0)) / 3.0).alias("completeness_score"),
            
            # Calculate overall data quality score
            ((when(col("invoice_id").isNotNull() & (col("invoice_id") != ""), 1).otherwise(0) +
              when(col("invoice_type").isNotNull() & (col("invoice_type") != ""), 1).otherwise(0) +
              when(col("status").isNotNull() & (col("status") != ""), 1).otherwise(0) +
              when(col("reference_number").isNotNull() & (col("reference_number") != ""), 1).otherwise(0)) / 4.0).alias("data_quality_score"),
            
            # Add audit columns
            current_timestamp().alias("created_timestamp"),
            current_timestamp().alias("modified_timestamp"),
            current_timestamp().alias("processed_at"),
            lit("bronze_layer").alias("source_system")
        )
        
        logger.info(f"Transformation completed. Columns: {len(df.columns)}")
        return df
        
    except Exception as e:
        logger.error(f"Error in transformation: {str(e)}")
        raise

# COMMAND ----------
# Main Processing Function (can be reused in other silver notebooks)
@log_execution(logger)
def process_bronze_to_silver(spark, table_name, table_config, catalog, bronze_schema, silver_schema,
                           bronze_path, silver_path, logger, processing_date=None, custom_transform_func=None):
    """
    Generic function to process any table from bronze to silver layer using silver_utils functions.
    """
    logger.info(f"🚀 Starting bronze to silver processing for {table_name}")

    try:
        # Step 1: Read bronze data using silver_utils function
        bronze_df = read_bronze_data(spark, table_name, catalog, bronze_schema, bronze_path, logger, processing_date)
        if bronze_df.count() == 0:
            logger.warning(f"No bronze data found for {table_name}")
            return None

        # Step 2: Apply column mapping using silver_utils function
        mapped_df = apply_column_mapping(bronze_df, table_name, table_config, logger)

        # Step 3: Apply transformations
        if custom_transform_func:
            logger.info("Applying custom transformation function")
            silver_df = custom_transform_func(mapped_df, table_name, table_config, logger)
        else:
            logger.info("Using table-specific transformation function")
            silver_df = transform_data(mapped_df, table_name, table_config, logger)

        # Step 4: Write to silver layer using silver_utils function
        write_silver_data(spark, silver_df, table_name, table_config, catalog, silver_schema, silver_path, logger)

        logger.info(f"✅ Successfully processed {silver_df.count()} records for {table_name}")
        return silver_df

    except Exception as e:
        logger.error(f"❌ Error processing {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Main execution
logger.info(f"Starting bronze to silver processing for {table_name}")

# Get processing date from configuration
processing_date = pipeline_config.get("default_processing_date")

# Process data using configuration
try:
    result_df = process_bronze_to_silver(
        spark=spark,
        table_name=table_name,
        table_config=table_config,
        catalog=catalog,
        bronze_schema=bronze_schema,
        silver_schema=silver_schema,
        bronze_path=bronze_path,
        silver_path=silver_path,
        logger=logger,
        processing_date=processing_date
    )
    if result_df is not None:
        logger.info(f"✅ Completed bronze to silver processing successfully for {table_name}")
        log_dataframe_info(result_df, f"{table_name}_silver_final", logger)
    else:
        logger.warning(f"⚠️ No data processed for {table_name}")
except Exception as e:
    logger.error(f"❌ Failed to process {table_name}: {str(e)}")
    raise
