# Databricks notebook source
# MAGIC %run ../../start_up

# COMMAND ----------

# MAGIC %md
# MAGIC # Item Silver Layer Processing
# MAGIC
# MAGIC This notebook processes item data from bronze to silver layer.
# MAGIC It uses the generic utility functions from `utils/silver_utils.py` for:
# MAGIC - Reading bronze data (`read_bronze_data`)
# MAGIC - Writing silver data with schema management (`write_silver_data`)
# MAGIC - Creating/updating Delta tables (`create_or_update_silver_table`)
# MAGIC - Applying column mappings (`apply_column_mapping`)
# MAGIC
# MAGIC The table-specific transformation logic is defined in this notebook.

# COMMAND ----------

# Imports
from pyspark.sql.functions import (
    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace, 
    to_date, regexp_extract, datediff
)
from pyspark.sql.types import StringType

# Initialize logger using configuration from startup
logger = create_logger(component_log_levels=component_log_levels)
logger.info("🚀 Initializing item silver layer processing")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
table_name = "dim_item"

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

logger.info(f"Configuration initialized with catalog: {catalog}, bronze_schema: {bronze_schema}, silver_schema: {silver_schema}")

# COMMAND ----------
# Simple Transformation Functions
@log_execution(logger)
def transform_data(df, table_name, table_config, logger):
    """Transform item data with item-specific business logic."""
    logger.info(f"Applying item-specific transformations for {table_name}")

    try:
        # Note: Column mapping is now handled by apply_column_mapping function
        # This function focuses on item-specific transformations
        
        # Apply all transformations using SQL expressions for simplicity
        df = df.select(
            # Core item fields
            col("item_id"),
            col("product_id"),
            trim(regexp_replace(col("item_name"), r"\s+", " ")).alias("item_name"),
            
            # Standardize unit of measure
            when(upper(col("unit_of_measure")).isin(["EA", "EACH"]), "Each")
            .when(upper(col("unit_of_measure")).isin(["KG", "KILOGRAM"]), "Kilogram")
            .when(upper(col("unit_of_measure")).isin(["G", "GRAM"]), "Gram")
            .when(upper(col("unit_of_measure")).isin(["L", "LITER", "LITRE"]), "Liter")
            .when(upper(col("unit_of_measure")).isin(["ML", "MILLILITER"]), "Milliliter")
            .when(upper(col("unit_of_measure")).isin(["LB", "POUND"]), "Pound")
            .when(upper(col("unit_of_measure")).isin(["OZ", "OUNCE"]), "Ounce")
            .otherwise(col("unit_of_measure")).alias("unit_of_measure"),
            
            # Clean package size
            trim(col("package_size")).alias("package_size"),
            
            # Clean measurement
            trim(col("measurement")).alias("measurement"),
            
            # Clean nutritional info
            trim(col("nutritional_info")).alias("nutritional_info"),
            
            # Convert dates
            to_date(col("manufacture_date"), "yyyy-MM-dd").alias("manufacture_date"),
            to_date(col("expiry_date"), "yyyy-MM-dd").alias("expiry_date"),
            
            # Convert is_active to boolean
            when(upper(col("is_active")).isin(["TRUE", "1", "ACTIVE", "Y", "YES"]), True)
            .when(upper(col("is_active")).isin(["FALSE", "0", "INACTIVE", "N", "NO"]), False)
            .otherwise(None).alias("is_active"),
            
            # Add validation columns
            when(col("manufacture_date").isNotNull() & col("expiry_date").isNotNull(),
                 when(col("manufacture_date") <= col("expiry_date"), True).otherwise(False)
            ).otherwise(None).alias("date_logic_valid"),
            
            when(col("expiry_date").isNotNull(),
                 when(col("expiry_date") >= current_timestamp().cast("date"), True).otherwise(False)
            ).otherwise(None).alias("not_expired"),
            
            # Data quality validation columns
            when(col("item_id").isNotNull() & (col("item_id") != ""), True).otherwise(False).alias("primary_key_valid"),
            when(col("item_id").isNotNull() & (col("item_id") != ""), True).otherwise(False).alias("item_id_not_null_valid"),
            when(col("product_id").isNotNull() & (col("product_id") != ""), True).otherwise(False).alias("product_id_not_null_valid"),
            when(col("item_name").isNotNull() & (col("item_name") != ""), True).otherwise(False).alias("item_name_not_null_valid"),
            
            # Calculate completeness score based on required fields
            ((when(col("item_id").isNotNull() & (col("item_id") != ""), 1).otherwise(0) +
              when(col("product_id").isNotNull() & (col("product_id") != ""), 1).otherwise(0) +
              when(col("item_name").isNotNull() & (col("item_name") != ""), 1).otherwise(0)) / 3.0).alias("completeness_score"),
            
            # Calculate overall data quality score
            ((when(col("item_id").isNotNull() & (col("item_id") != ""), 1).otherwise(0) +
              when(col("product_id").isNotNull() & (col("product_id") != ""), 1).otherwise(0) +
              when(col("item_name").isNotNull() & (col("item_name") != ""), 1).otherwise(0) +
              when(col("unit_of_measure").isNotNull() & (col("unit_of_measure") != ""), 1).otherwise(0)) / 4.0).alias("data_quality_score"),
            
            # Add audit columns
            current_timestamp().alias("created_timestamp"),
            current_timestamp().alias("modified_timestamp"),
            current_timestamp().alias("processed_at"),
            lit("bronze_layer").alias("source_system")
        )
        
        logger.info(f"Transformation completed. Columns: {len(df.columns)}")
        return df
        
    except Exception as e:
        logger.error(f"Error in transformation: {str(e)}")
        raise

# COMMAND ----------
# Main Processing Function (can be reused in other silver notebooks)
@log_execution(logger)
def process_bronze_to_silver(spark, table_name, table_config, catalog, bronze_schema, silver_schema,
                           bronze_path, silver_path, logger, processing_date=None, custom_transform_func=None):
    """
    Generic function to process any table from bronze to silver layer using silver_utils functions.

    Args:
        spark: Spark session
        table_name: Name of the silver table (e.g., 'dim_item', 'dim_product')
        table_config: Table configuration dictionary
        catalog: Catalog name
        bronze_schema: Bronze schema name
        silver_schema: Silver schema name
        bronze_path: Bronze storage path
        silver_path: Silver storage path
        logger: Logger instance
        processing_date: Optional processing date for partitioned data
        custom_transform_func: Optional custom transformation function that takes (df, table_name, table_config, logger)

    Returns:
        Transformed DataFrame
    """
    logger.info(f"🚀 Starting bronze to silver processing for {table_name}")

    try:
        # Step 1: Read bronze data using silver_utils function
        bronze_df = read_bronze_data(spark, table_name, catalog, bronze_schema, bronze_path, logger, processing_date)
        if bronze_df.count() == 0:
            logger.warning(f"No bronze data found for {table_name}")
            return None

        # Step 2: Apply column mapping using silver_utils function
        mapped_df = apply_column_mapping(bronze_df, table_name, table_config, logger)

        # Step 3: Apply transformations
        if custom_transform_func:
            logger.info("Applying custom transformation function")
            silver_df = custom_transform_func(mapped_df, table_name, table_config, logger)
        else:
            logger.info("Using table-specific transformation function")
            # Use the table-specific transform_data function for item
            silver_df = transform_data(mapped_df, table_name, table_config, logger)

        # Step 4: Write to silver layer using silver_utils function
        write_silver_data(spark, silver_df, table_name, table_config, catalog, silver_schema, silver_path, logger)

        logger.info(f"✅ Successfully processed {silver_df.count()} records for {table_name}")
        return silver_df

    except Exception as e:
        logger.error(f"❌ Error processing {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Main execution
logger.info(f"Starting bronze to silver processing for {table_name}")

# Get processing date from configuration
processing_date = pipeline_config.get("default_processing_date")

# Process data using configuration
try:
    result_df = process_bronze_to_silver(
        spark=spark,
        table_name=table_name,
        table_config=table_config,
        catalog=catalog,
        bronze_schema=bronze_schema,
        silver_schema=silver_schema,
        bronze_path=bronze_path,
        silver_path=silver_path,
        logger=logger,
        processing_date=processing_date
    )
    if result_df is not None:
        logger.info(f"✅ Completed bronze to silver processing successfully for {table_name}")
        log_dataframe_info(result_df, f"{table_name}_silver_final", logger)
    else:
        logger.warning(f"⚠️ No data processed for {table_name}")
except Exception as e:
    logger.error(f"❌ Failed to process {table_name}: {str(e)}")
    raise
